<!-- 通知服务演示组件 -->
<template>
  <div class="p-6 max-w-4xl mx-auto">
    <h1 class="text-2xl font-bold mb-6">Ant Design Vue 全局提示组件演示</h1>
    
    <!-- 消息提示演示 -->
    <div class="mb-8">
      <h2 class="text-lg font-semibold mb-4">消息提示 (Message)</h2>
      <div class="flex gap-3 flex-wrap">
        <a-button type="primary" @click="showSuccess">
          成功消息
        </a-button>
        <a-button @click="showInfo">
          信息消息
        </a-button>
        <a-button type="default" @click="showWarning">
          警告消息
        </a-button>
        <a-button danger @click="showError">
          错误消息
        </a-button>
        <a-button type="dashed" @click="showLoading">
          加载消息
        </a-button>
      </div>
    </div>

    <!-- 通知提醒演示 -->
    <div class="mb-8">
      <h2 class="text-lg font-semibold mb-4">通知提醒 (Notification)</h2>
      <div class="flex gap-3 flex-wrap">
        <a-button type="primary" @click="showNotifySuccess">
          成功通知
        </a-button>
        <a-button @click="showNotifyInfo">
          信息通知
        </a-button>
        <a-button type="default" @click="showNotifyWarning">
          警告通知
        </a-button>
        <a-button danger @click="showNotifyError">
          错误通知
        </a-button>
      </div>
    </div>

    <!-- 错误处理演示 -->
    <div class="mb-8">
      <h2 class="text-lg font-semibold mb-4">智能错误处理</h2>
      <div class="flex gap-3 flex-wrap">
        <a-button @click="showLowError">
          低级错误
        </a-button>
        <a-button @click="showMediumError">
          中级错误
        </a-button>
        <a-button @click="showHighError">
          高级错误
        </a-button>
        <a-button danger @click="showCriticalError">
          严重错误
        </a-button>
        <a-button type="dashed" @click="showRetryableError">
          可重试错误
        </a-button>
      </div>
    </div>

    <!-- 批量演示 -->
    <div class="mb-8">
      <h2 class="text-lg font-semibold mb-4">批量演示</h2>
      <div class="flex gap-3 flex-wrap">
        <a-button type="primary" @click="showSequentialDemo">
          顺序演示
        </a-button>
        <a-button danger @click="clearAll">
          清除所有
        </a-button>
      </div>
    </div>

    <!-- 使用说明 -->
    <div class="bg-gray-50 p-4 rounded-lg">
      <h3 class="text-md font-semibold mb-2">使用说明</h3>
      <ul class="text-sm text-gray-600 space-y-1">
        <li>• <strong>消息提示</strong>：适用于简短的操作反馈，显示在页面顶部</li>
        <li>• <strong>通知提醒</strong>：适用于重要信息，支持标题和详细描述</li>
        <li>• <strong>智能错误处理</strong>：根据错误严重程度自动选择提示方式</li>
        <li>• <strong>重试机制</strong>：支持为错误提示添加重试按钮</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  notificationService, 
  ErrorType, 
  ErrorSeverity 
} from '../services/notificationService'

// 消息提示演示
const showSuccess = () => {
  notificationService.success('操作成功！')
}

const showInfo = () => {
  notificationService.info('这是一条信息提示')
}

const showWarning = () => {
  notificationService.warning('请注意检查您的输入')
}

const showError = () => {
  notificationService.error('操作失败，请重试')
}

const showLoading = () => {
  const hide = notificationService.loading('正在处理中...')
  // 3秒后自动关闭
  setTimeout(() => {
    hide()
    notificationService.success('处理完成！')
  }, 3000)
}

// 通知提醒演示
const showNotifySuccess = () => {
  notificationService.notifySuccess(
    '操作成功', 
    '您的数据已成功保存到服务器'
  )
}

const showNotifyInfo = () => {
  notificationService.notifyInfo(
    '系统提示', 
    '您有3条新消息等待查看'
  )
}

const showNotifyWarning = () => {
  notificationService.notifyWarning(
    '会话提醒', 
    '您的登录会话将在5分钟后过期，请及时保存数据'
  )
}

const showNotifyError = () => {
  notificationService.notifyError(
    '连接错误', 
    '无法连接到服务器，请检查您的网络连接'
  )
}

// 错误处理演示
const showLowError = () => {
  notificationService.handleError({
    type: ErrorType.VALIDATION,
    severity: ErrorSeverity.LOW,
    message: '输入格式不正确'
  })
}

const showMediumError = () => {
  notificationService.handleError({
    type: ErrorType.API,
    severity: ErrorSeverity.MEDIUM,
    message: 'API调用失败',
    details: '服务器返回了错误响应'
  })
}

const showHighError = () => {
  notificationService.handleError({
    type: ErrorType.NETWORK,
    severity: ErrorSeverity.HIGH,
    message: '网络连接失败',
    details: '无法连接到远程服务器，请检查网络设置'
  })
}

const showCriticalError = () => {
  notificationService.handleError({
    type: ErrorType.AUTH,
    severity: ErrorSeverity.CRITICAL,
    message: '认证失败',
    details: '您的登录凭证已过期，需要重新登录'
  })
}

const showRetryableError = () => {
  notificationService.handleError({
    type: ErrorType.WEBSOCKET,
    severity: ErrorSeverity.MEDIUM,
    message: 'WebSocket连接断开',
    details: '实时通信连接已断开',
    retryable: true,
    action: () => {
      notificationService.success('重连成功！')
    },
    actionText: '重新连接'
  })
}

// 顺序演示
const showSequentialDemo = () => {
  notificationService.info('开始演示...')
  
  setTimeout(() => {
    notificationService.success('第一步完成')
  }, 1000)
  
  setTimeout(() => {
    notificationService.notifyInfo('进度更新', '正在执行第二步...')
  }, 2000)
  
  setTimeout(() => {
    notificationService.warning('注意事项')
  }, 3000)
  
  setTimeout(() => {
    notificationService.notifySuccess('演示完成', '所有步骤已成功执行')
  }, 4000)
}

// 清除所有提示
const clearAll = () => {
  notificationService.clearAll()
  notificationService.info('已清除所有提示')
}
</script>
