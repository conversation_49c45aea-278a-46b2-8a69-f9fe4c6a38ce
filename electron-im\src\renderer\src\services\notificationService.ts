// 通知服务 - 统一的错误处理和用户提示
import { notification, message } from 'ant-design-vue'
import type { NotificationArgsProps } from 'ant-design-vue'
import { h } from 'vue'

// 错误类型枚举
export enum ErrorType {
  NETWORK = 'network',
  WEBSOCKET = 'websocket',
  AUTH = 'auth',
  VALIDATION = 'validation',
  MESSAGE_SEND = 'message_send',
  API = 'api',
  UNKNOWN = 'unknown'
}

// 错误严重程度
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// 错误信息接口
export interface ErrorInfo {
  type: ErrorType
  severity: ErrorSeverity
  message: string
  details?: string
  code?: string | number
  timestamp?: number
  retryable?: boolean
  action?: () => void
  actionText?: string
}

// 通知配置接口
export interface NotificationConfig {
  duration?: number
  placement?: 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight'
  showProgress?: boolean
}

// 消息配置接口
export interface MessageConfig {
  duration?: number
  maxCount?: number
}

class NotificationService {
  private defaultNotificationConfig: NotificationConfig = {
    duration: 4.5,
    placement: 'topRight',
    showProgress: true
  }

  private defaultMessageConfig: MessageConfig = {
    duration: 3,
    maxCount: 3
  }

  constructor() {
    // 配置全局消息设置
    message.config({
      duration: this.defaultMessageConfig.duration,
      maxCount: this.defaultMessageConfig.maxCount,
      top: '20px'
    })

    // 配置全局通知设置
    notification.config({
      placement: this.defaultNotificationConfig.placement,
      duration: this.defaultNotificationConfig.duration,
      top: '24px',
      bottom: '24px'
    })
  }

  // 显示成功消息
  success(content: string, config?: MessageConfig): void {
    message.success({
      content,
      duration: config?.duration || this.defaultMessageConfig.duration
    })
  }

  // 显示信息消息
  info(content: string, config?: MessageConfig): void {
    message.info({
      content,
      duration: config?.duration || this.defaultMessageConfig.duration
    })
  }

  // 显示警告消息
  warning(content: string, config?: MessageConfig): void {
    message.warning({
      content,
      duration: config?.duration || this.defaultMessageConfig.duration
    })
  }

  // 显示错误消息
  error(content: string, config?: MessageConfig): void {
    message.error({
      content,
      duration: config?.duration || this.defaultMessageConfig.duration
    })
  }

  // 显示加载消息
  loading(content: string, config?: MessageConfig): () => void {
    return message.loading({
      content,
      duration: config?.duration || 0 // 默认不自动关闭
    })
  }

  // 显示成功通知
  notifySuccess(title: string, description?: string, config?: NotificationConfig): void {
    notification.success({
      message: title,
      description,
      duration: config?.duration || this.defaultNotificationConfig.duration,
      placement: config?.placement || this.defaultNotificationConfig.placement
    })
  }

  // 显示信息通知
  notifyInfo(title: string, description?: string, config?: NotificationConfig): void {
    notification.info({
      message: title,
      description,
      duration: config?.duration || this.defaultNotificationConfig.duration,
      placement: config?.placement || this.defaultNotificationConfig.placement
    })
  }

  // 显示警告通知
  notifyWarning(title: string, description?: string, config?: NotificationConfig): void {
    notification.warning({
      message: title,
      description,
      duration: config?.duration || this.defaultNotificationConfig.duration,
      placement: config?.placement || this.defaultNotificationConfig.placement
    })
  }

  // 显示错误通知
  notifyError(title: string, description?: string, config?: NotificationConfig): void {
    notification.error({
      message: title,
      description,
      duration: config?.duration || this.defaultNotificationConfig.duration,
      placement: config?.placement || this.defaultNotificationConfig.placement
    })
  }

  // 统一错误处理方法
  handleError(errorInfo: ErrorInfo): void {
    const {
      type,
      severity,
      message: errorMessage,
      details,
      retryable,
      action,
      actionText
    } = errorInfo

    // 根据严重程度决定使用消息还是通知
    const shouldUseNotification =
      severity === ErrorSeverity.HIGH || severity === ErrorSeverity.CRITICAL

    if (shouldUseNotification) {
      // 使用通知显示详细错误信息
      const notificationConfig: NotificationArgsProps = {
        message: this.getErrorTitle(type, severity),
        description: errorMessage + (details ? `\n${details}` : ''),
        duration: severity === ErrorSeverity.CRITICAL ? 0 : this.getErrorDuration(severity),
        placement: this.defaultNotificationConfig.placement
      }

      // 添加重试按钮
      if (retryable && action) {
        notificationConfig.btn = h(
          'a-button',
          {
            type: 'primary',
            size: 'small',
            onClick: action
          },
          actionText || '重试'
        )
      }

      notification.error(notificationConfig)
    } else {
      // 使用消息显示简单错误信息
      this.error(errorMessage, {
        duration: this.getErrorDuration(severity)
      })
    }
  }

  // 获取错误标题
  private getErrorTitle(type: ErrorType, severity: ErrorSeverity): string {
    const severityText = {
      [ErrorSeverity.LOW]: '提示',
      [ErrorSeverity.MEDIUM]: '警告',
      [ErrorSeverity.HIGH]: '错误',
      [ErrorSeverity.CRITICAL]: '严重错误'
    }

    const typeText = {
      [ErrorType.NETWORK]: '网络',
      [ErrorType.WEBSOCKET]: 'WebSocket',
      [ErrorType.AUTH]: '认证',
      [ErrorType.VALIDATION]: '验证',
      [ErrorType.MESSAGE_SEND]: '消息发送',
      [ErrorType.API]: 'API',
      [ErrorType.UNKNOWN]: '未知'
    }

    return `${typeText[type]}${severityText[severity]}`
  }

  // 获取错误显示时长
  private getErrorDuration(severity: ErrorSeverity): number {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
        return 0 // 不自动关闭
      case ErrorSeverity.HIGH:
        return 8
      case ErrorSeverity.MEDIUM:
        return 6
      case ErrorSeverity.LOW:
        return 4
      default:
        return 4.5
    }
  }

  // 清除所有通知
  clearAll(): void {
    notification.destroy()
    message.destroy()
  }

  // 清除所有消息
  clearMessages(): void {
    message.destroy()
  }

  // 清除所有通知
  clearNotifications(): void {
    notification.destroy()
  }
}

// 导出单例实例
export const notificationService = new NotificationService()

// 便捷方法导出
export const {
  success,
  info,
  warning,
  error,
  loading,
  notifySuccess,
  notifyInfo,
  notifyWarning,
  notifyError,
  handleError,
  clearAll,
  clearMessages,
  clearNotifications
} = notificationService
