<!-- 消息重试功能演示组件 -->
<template>
  <div class="p-6 max-w-4xl mx-auto space-y-6">
    <h1 class="text-2xl font-bold mb-6">消息重试功能演示</h1>
    
    <!-- 模拟消息列表 -->
    <div class="space-y-4">
      <h2 class="text-lg font-semibold">消息状态演示</h2>
      
      <!-- 正常发送成功的消息 -->
      <div class="flex items-start gap-3 justify-end">
        <div class="flex-1 flex flex-col items-end">
          <div class="flex items-center gap-2 mb-2 text-right">
            <span class="text-xs text-gray-400 font-normal">
              {{ formatTime(new Date()) }}
            </span>
            <span class="text-xs text-gray-600 font-medium">我</span>
          </div>
          <div class="bg-[#c9daf5] text-white rounded-lg p-3 max-w-md shadow-sm">
            <div class="text-sm leading-relaxed text-gray-900 break-words whitespace-pre-wrap">
              这是一条正常发送成功的消息
            </div>
          </div>
        </div>
        <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium">
          我
        </div>
      </div>
      
      <!-- 发送中的消息 -->
      <div class="flex items-start gap-3 justify-end">
        <div class="flex-1 flex flex-col items-end">
          <div class="flex items-center gap-2 mb-2 text-right">
            <div class="flex items-center gap-1">
              <div class="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin"></div>
              <span class="text-xs text-gray-400">发送中...</span>
            </div>
            <span class="text-xs text-gray-400 font-normal">
              {{ formatTime(new Date()) }}
            </span>
            <span class="text-xs text-gray-600 font-medium">我</span>
          </div>
          <div class="bg-[#c9daf5] text-white rounded-lg p-3 max-w-md shadow-sm">
            <div class="text-sm leading-relaxed text-gray-900 break-words whitespace-pre-wrap">
              这是一条正在发送中的消息...
            </div>
          </div>
        </div>
        <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium">
          我
        </div>
      </div>
      
      <!-- 发送失败的消息 (带重试按钮) -->
      <div class="flex items-start gap-3 justify-end">
        <!-- 重试按钮 -->
        <div class="flex items-center">
          <button
            @click="handleRetry"
            class="w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-colors duration-200"
            title="重新发送"
          >
            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
        
        <div class="flex-1 flex flex-col items-end">
          <div class="flex items-center gap-2 mb-2 text-right">
            <div class="flex items-center gap-1">
              <svg class="w-3 h-3 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
              <span class="text-xs text-red-500">发送失败</span>
            </div>
            <span class="text-xs text-gray-400 font-normal">
              {{ formatTime(new Date()) }}
            </span>
            <span class="text-xs text-gray-600 font-medium">我</span>
          </div>
          <div class="bg-red-50 border border-red-200 rounded-lg p-3 max-w-md shadow-sm">
            <div class="text-sm leading-relaxed text-red-700 break-words whitespace-pre-wrap">
              这是一条发送失败的消息，点击左侧红色按钮可以重试
            </div>
          </div>
        </div>
        <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium">
          我
        </div>
      </div>
      
      <!-- 接收到的消息 (正常显示) -->
      <div class="flex items-start gap-3">
        <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-medium">
          张
        </div>
        <div class="flex flex-col">
          <div class="flex items-center gap-2 mb-2">
            <span class="text-xs text-gray-600 font-medium">张三</span>
            <span class="text-xs text-gray-400 font-normal">
              {{ formatTime(new Date()) }}
            </span>
          </div>
          <div class="bg-[#ffffff] border border-gray-200 rounded-lg p-3 shadow-sm max-w-md">
            <div class="text-sm text-gray-900 leading-relaxed break-words whitespace-pre-wrap">
              这是对方发来的消息，正常显示
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 功能测试区域 -->
    <div class="space-y-4">
      <h2 class="text-lg font-semibold">功能测试</h2>
      
      <div class="flex gap-3">
        <a-button @click="simulateSendingMessage" type="primary">
          模拟发送消息
        </a-button>
        <a-button @click="simulateFailedMessage" danger>
          模拟发送失败
        </a-button>
        <a-button @click="clearMessages">
          清空消息
        </a-button>
      </div>
      
      <!-- 动态消息列表 -->
      <div class="space-y-3 max-h-96 overflow-y-auto border rounded-lg p-4">
        <div v-if="!testMessages.length" class="text-center text-gray-500 py-8">
          暂无测试消息，点击上方按钮开始测试
        </div>
        
        <div 
          v-for="msg in testMessages" 
          :key="msg.id"
          class="flex items-start gap-3 justify-end"
        >
          <!-- 重试按钮 (仅在发送失败时显示) -->
          <div v-if="msg.sendError" class="flex items-center">
            <button
              @click="retryTestMessage(msg)"
              class="w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-colors duration-200"
              title="重新发送"
            >
              <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
          
          <div class="flex-1 flex flex-col items-end">
            <div class="flex items-center gap-2 mb-2 text-right">
              <!-- 发送状态指示 -->
              <div v-if="msg.isSending" class="flex items-center gap-1">
                <div class="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                <span class="text-xs text-gray-400">发送中...</span>
              </div>
              <div v-else-if="msg.sendError" class="flex items-center gap-1">
                <svg class="w-3 h-3 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
                <span class="text-xs text-red-500">发送失败</span>
              </div>
              <span class="text-xs text-gray-400 font-normal">
                {{ formatTime(msg.timestamp) }}
              </span>
              <span class="text-xs text-gray-600 font-medium">我</span>
            </div>
            <div 
              :class="[
                'rounded-lg p-3 max-w-md shadow-sm',
                msg.sendError 
                  ? 'bg-red-50 border border-red-200' 
                  : 'bg-[#c9daf5] text-white'
              ]"
            >
              <div
                :class="[
                  'text-sm leading-relaxed break-words whitespace-pre-wrap',
                  msg.sendError ? 'text-red-700' : 'text-gray-900'
                ]"
                v-text="msg.content"
              ></div>
            </div>
          </div>
          <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium">
            我
          </div>
        </div>
      </div>
    </div>
    
    <!-- 说明 -->
    <div class="bg-gray-50 p-4 rounded-lg">
      <h3 class="text-md font-semibold mb-2">功能说明</h3>
      <ul class="text-sm text-gray-600 space-y-1">
        <li>• <strong>发送中状态</strong>：显示旋转加载图标和"发送中..."文字</li>
        <li>• <strong>发送失败状态</strong>：消息气泡变为红色边框，左侧显示红色重试按钮</li>
        <li>• <strong>重试功能</strong>：点击红色重试按钮可以重新发送失败的消息</li>
        <li>• <strong>状态指示</strong>：消息旁边显示相应的状态图标和文字</li>
        <li>• <strong>视觉反馈</strong>：失败消息使用不同的颜色主题以便区分</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { notificationService } from '../services/notificationService'

interface TestMessage {
  id: string
  content: string
  timestamp: Date
  isSending?: boolean
  sendError?: string
}

const testMessages = ref<TestMessage[]>([])

// 时间格式化
const formatTime = (date: Date) => {
  return date.toLocaleString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 模拟发送消息
const simulateSendingMessage = () => {
  const message: TestMessage = {
    id: Date.now().toString(),
    content: '这是一条测试消息',
    timestamp: new Date(),
    isSending: true
  }
  
  testMessages.value.push(message)
  
  // 模拟发送过程
  setTimeout(() => {
    const index = testMessages.value.findIndex(m => m.id === message.id)
    if (index !== -1) {
      testMessages.value[index].isSending = false
      notificationService.success('消息发送成功')
    }
  }, 2000)
}

// 模拟发送失败
const simulateFailedMessage = () => {
  const message: TestMessage = {
    id: Date.now().toString(),
    content: '这是一条会发送失败的消息',
    timestamp: new Date(),
    isSending: true
  }
  
  testMessages.value.push(message)
  
  // 模拟发送失败
  setTimeout(() => {
    const index = testMessages.value.findIndex(m => m.id === message.id)
    if (index !== -1) {
      testMessages.value[index].isSending = false
      testMessages.value[index].sendError = '网络连接失败'
      notificationService.error('消息发送失败')
    }
  }, 2000)
}

// 重试测试消息
const retryTestMessage = (message: TestMessage) => {
  const index = testMessages.value.findIndex(m => m.id === message.id)
  if (index !== -1) {
    testMessages.value[index].sendError = undefined
    testMessages.value[index].isSending = true
    
    // 模拟重试过程 (50% 成功率)
    setTimeout(() => {
      const msgIndex = testMessages.value.findIndex(m => m.id === message.id)
      if (msgIndex !== -1) {
        testMessages.value[msgIndex].isSending = false
        
        if (Math.random() > 0.5) {
          notificationService.success('消息重发成功')
        } else {
          testMessages.value[msgIndex].sendError = '重发失败'
          notificationService.error('消息重发失败')
        }
      }
    }, 1500)
  }
}

// 演示重试功能
const handleRetry = () => {
  notificationService.info('点击了重试按钮')
}

// 清空消息
const clearMessages = () => {
  testMessages.value = []
  notificationService.info('已清空测试消息')
}
</script>
