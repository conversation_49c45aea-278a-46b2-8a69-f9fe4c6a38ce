# 消息重试功能说明

## 概述

本功能为聊天系统添加了完整的消息发送失败重试机制，包括：
- 消息发送状态显示
- 发送失败时的视觉反馈
- 红色重试按钮
- 自动提示和错误处理

## 功能特性

### 1. 消息状态显示

#### 发送中状态
- 显示旋转加载图标
- 显示"发送中..."文字提示
- 消息气泡保持正常样式

#### 发送成功状态
- 移除所有状态指示器
- 消息气泡显示正常蓝色样式
- 无额外标识

#### 发送失败状态
- 显示红色错误图标
- 显示"发送失败"文字提示
- 消息气泡变为红色边框样式
- 左侧显示红色重试按钮

### 2. 重试机制

#### 重试按钮
- 位置：失败消息左侧
- 样式：红色圆形按钮，带重试图标
- 交互：悬停时颜色加深
- 提示：鼠标悬停显示"重新发送"

#### 重试流程
1. 点击重试按钮
2. 清除错误状态，设置为发送中
3. 重新调用发送接口
4. 根据结果更新状态

### 3. 视觉设计

#### 正常消息
```css
背景色: #c9daf5 (蓝色)
文字色: #1f2937 (深灰)
边框: 无
```

#### 失败消息
```css
背景色: #fef2f2 (浅红)
文字色: #b91c1c (深红)
边框: #fecaca (红色边框)
```

#### 重试按钮
```css
背景色: #ef4444 (红色)
悬停色: #dc2626 (深红)
尺寸: 24x24px
图标: 重试箭头
```

## 技术实现

### 1. 数据结构

消息对象包含以下状态字段：

```typescript
interface Message {
  id: string
  senderId: string
  senderName: string
  content: string
  timestamp: Date
  isSending?: boolean    // 是否正在发送
  sendError?: string     // 发送错误信息
}
```

### 2. 状态管理

在 `messageStore` 中实现：

```typescript
// 发送消息
const sendMessage = async (receiverId: string, content: string) => {
  // 1. 创建临时消息，设置 isSending: true
  // 2. 添加到界面显示
  // 3. 调用发送接口
  // 4. 根据结果更新状态
}

// 重试消息
const retryMessage = async (userId: string, messageId: string) => {
  // 1. 清除错误状态
  // 2. 设置为发送中
  // 3. 重新发送
  // 4. 更新状态
}
```

### 3. UI 组件

在 `MessageList.vue` 中实现：

```vue
<!-- 重试按钮 -->
<div v-if="message.sendError" class="flex items-center">
  <button @click="retryMessage(message)" class="retry-button">
    <!-- 重试图标 -->
  </button>
</div>

<!-- 状态指示 -->
<div v-if="message.isSending" class="sending-indicator">
  <!-- 加载动画 -->
</div>
<div v-else-if="message.sendError" class="error-indicator">
  <!-- 错误图标 -->
</div>

<!-- 消息气泡 -->
<div :class="message.sendError ? 'error-bubble' : 'normal-bubble'">
  <!-- 消息内容 -->
</div>
```

## 使用方法

### 1. 正常发送消息

```typescript
// 在 Chat.vue 中
const sendMessage = async (content: string) => {
  try {
    await messageStore.sendMessage(currentChatId.value, content)
    // 发送成功，无需额外处理
  } catch (error) {
    // 错误已在 store 中处理，会自动显示重试按钮
  }
}
```

### 2. 处理重试

```typescript
// 在 MessageList.vue 中
const retryMessage = async (message: Message) => {
  try {
    const success = await messageStore.retryMessage(
      props.currentContact.id, 
      message.id
    )
    if (success) {
      notificationService.success('消息重发成功')
    }
  } catch (error) {
    notificationService.error('消息重发失败')
  }
}
```

## 错误处理

### 1. 网络错误
- 显示"网络连接失败"
- 提供重试按钮
- 支持多次重试

### 2. 服务器错误
- 显示"服务器错误"
- 提供重试按钮
- 记录错误日志

### 3. 认证错误
- 显示"认证失败"
- 不提供重试（需要重新登录）
- 跳转到登录页面

## 用户体验

### 1. 即时反馈
- 发送时立即显示消息
- 状态变化有动画过渡
- 错误提示清晰明确

### 2. 操作便捷
- 重试按钮位置醒目
- 一键重试，无需重新输入
- 支持多次重试

### 3. 视觉清晰
- 不同状态有明显区分
- 颜色语义化设计
- 图标直观易懂

## 测试建议

### 1. 功能测试
- 测试正常发送流程
- 测试网络断开时的失败处理
- 测试重试功能的成功和失败情况
- 测试多条消息同时发送失败的情况

### 2. 界面测试
- 验证不同状态的视觉效果
- 测试按钮的交互反馈
- 检查在不同屏幕尺寸下的显示

### 3. 边界测试
- 测试极长消息的重试
- 测试快速连续重试
- 测试网络恢复后的自动重试

## 演示组件

项目中包含 `MessageRetryDemo.vue` 组件，可以用来：
- 演示各种消息状态
- 测试重试功能
- 验证视觉效果
- 进行功能调试

使用方法：
```vue
<template>
  <MessageRetryDemo />
</template>

<script setup>
import MessageRetryDemo from '@/components/MessageRetryDemo.vue'
</script>
```

## 注意事项

1. **性能考虑**：避免频繁重试导致的性能问题
2. **用户体验**：重试失败时给出明确的错误提示
3. **数据一致性**：确保重试过程中消息状态的正确更新
4. **网络优化**：考虑网络状况自动调整重试策略
