// 通知服务使用示例

import { 
  notificationService, 
  ErrorType, 
  ErrorSeverity 
} from '../src/renderer/src/services/notificationService'

// 1. 基本消息提示
export function basicMessageExamples() {
  // 成功提示
  notificationService.success('数据保存成功！')
  
  // 信息提示
  notificationService.info('系统将在5分钟后维护')
  
  // 警告提示
  notificationService.warning('请检查输入的邮箱格式')
  
  // 错误提示
  notificationService.error('网络连接失败')
  
  // 加载提示
  const hideLoading = notificationService.loading('正在上传文件...')
  // 在适当的时候关闭加载提示
  setTimeout(() => {
    hideLoading()
    notificationService.success('文件上传完成！')
  }, 3000)
}

// 2. 通知提醒示例
export function notificationExamples() {
  // 成功通知
  notificationService.notifySuccess(
    '账户创建成功',
    '欢迎加入我们！您的账户已成功创建，现在可以开始使用所有功能。'
  )
  
  // 信息通知
  notificationService.notifyInfo(
    '新功能上线',
    '我们刚刚发布了聊天记录搜索功能，快去体验吧！'
  )
  
  // 警告通知
  notificationService.notifyWarning(
    '存储空间不足',
    '您的存储空间使用率已达到90%，建议清理一些不必要的文件。'
  )
  
  // 错误通知
  notificationService.notifyError(
    '同步失败',
    '无法同步您的聊天记录到云端，请检查网络连接后重试。'
  )
}

// 3. 智能错误处理示例
export function errorHandlingExamples() {
  // 网络错误处理
  async function handleNetworkError() {
    try {
      // 模拟网络请求
      await fetch('/api/data')
    } catch (error) {
      notificationService.handleError({
        type: ErrorType.NETWORK,
        severity: ErrorSeverity.MEDIUM,
        message: '网络请求失败',
        details: '无法连接到服务器，请检查网络连接',
        retryable: true,
        action: handleNetworkError,
        actionText: '重试'
      })
    }
  }
  
  // WebSocket连接错误
  function handleWebSocketError(error: Error) {
    notificationService.handleError({
      type: ErrorType.WEBSOCKET,
      severity: ErrorSeverity.HIGH,
      message: 'WebSocket连接断开',
      details: `连接错误: ${error.message}`,
      retryable: true,
      action: () => {
        // 重新连接WebSocket的逻辑
        console.log('正在重新连接WebSocket...')
        notificationService.success('WebSocket重连成功！')
      },
      actionText: '重新连接'
    })
  }
  
  // 认证错误
  function handleAuthError() {
    notificationService.handleError({
      type: ErrorType.AUTH,
      severity: ErrorSeverity.CRITICAL,
      message: '认证失败',
      details: '您的登录会话已过期，请重新登录',
      retryable: false
    })
  }
  
  // 表单验证错误
  function handleValidationError(field: string, message: string) {
    notificationService.handleError({
      type: ErrorType.VALIDATION,
      severity: ErrorSeverity.LOW,
      message: `${field}验证失败`,
      details: message
    })
  }
}

// 4. 在Vue组件中的使用示例
export const vueComponentExample = `
<template>
  <div>
    <a-button @click="handleSave" :loading="saving">
      保存数据
    </a-button>
    <a-button @click="handleUpload">
      上传文件
    </a-button>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { notificationService, ErrorType, ErrorSeverity } from '../services/notificationService'

const saving = ref(false)

// 保存数据示例
const handleSave = async () => {
  if (!validateForm()) {
    notificationService.warning('请填写所有必填字段')
    return
  }
  
  saving.value = true
  
  try {
    await saveData()
    notificationService.success('数据保存成功！')
  } catch (error) {
    notificationService.handleError({
      type: ErrorType.API,
      severity: ErrorSeverity.MEDIUM,
      message: '保存失败',
      details: error.message,
      retryable: true,
      action: handleSave,
      actionText: '重试保存'
    })
  } finally {
    saving.value = false
  }
}

// 文件上传示例
const handleUpload = async () => {
  const hideLoading = notificationService.loading('正在上传文件...')
  
  try {
    await uploadFile()
    hideLoading()
    notificationService.notifySuccess(
      '上传成功',
      '文件已成功上传到服务器'
    )
  } catch (error) {
    hideLoading()
    notificationService.handleError({
      type: ErrorType.API,
      severity: ErrorSeverity.MEDIUM,
      message: '文件上传失败',
      details: '请检查文件格式和大小',
      retryable: true,
      action: handleUpload,
      actionText: '重新上传'
    })
  }
}

// 表单验证
function validateForm(): boolean {
  // 验证逻辑
  return true
}

// 模拟API调用
async function saveData() {
  // 保存逻辑
}

async function uploadFile() {
  // 上传逻辑
}
</script>
`

// 5. 配置示例
export function configurationExamples() {
  // 自定义消息显示时长
  notificationService.success('快速提示', { duration: 2 })
  notificationService.error('重要错误', { duration: 10 })
  
  // 自定义通知位置和时长
  notificationService.notifyInfo(
    '系统通知',
    '这是一个自定义配置的通知',
    {
      duration: 8,
      placement: 'bottomRight'
    }
  )
}

// 6. 批量操作示例
export function batchOperationExample() {
  // 批量处理开始
  notificationService.info('开始批量处理...')
  
  const items = ['item1', 'item2', 'item3']
  let processed = 0
  
  items.forEach(async (item, index) => {
    try {
      await processItem(item)
      processed++
      
      if (processed === items.length) {
        notificationService.notifySuccess(
          '批量处理完成',
          \`成功处理了 \${processed} 个项目\`
        )
      }
    } catch (error) {
      notificationService.handleError({
        type: ErrorType.API,
        severity: ErrorSeverity.LOW,
        message: \`处理项目 \${index + 1} 失败\`,
        details: error.message
      })
    }
  })
}

async function processItem(item: string) {
  // 处理单个项目的逻辑
  return new Promise(resolve => setTimeout(resolve, 1000))
}
`
