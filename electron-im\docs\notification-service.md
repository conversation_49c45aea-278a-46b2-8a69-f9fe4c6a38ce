# 全局提示组件使用说明

## 概述

本项目已集成 Ant Design Vue 的全局提示组件，提供统一的用户反馈机制。包括消息提示（Message）和通知提醒（Notification）两种类型。

## 功能特性

- 🎯 **统一的API接口** - 简化的调用方式
- 🎨 **多种提示类型** - 成功、信息、警告、错误
- ⚡ **智能错误处理** - 根据错误严重程度自动选择提示方式
- 🔄 **重试机制** - 支持错误重试功能
- 🎛️ **可配置** - 支持自定义显示时长、位置等

## 基本使用

### 1. 导入服务

```typescript
import { notificationService } from '../services/notificationService'
```

### 2. 消息提示（Message）

适用于简短的操作反馈：

```typescript
// 成功消息
notificationService.success('操作成功！')

// 信息消息
notificationService.info('这是一条信息')

// 警告消息
notificationService.warning('请注意检查输入')

// 错误消息
notificationService.error('操作失败')

// 加载消息
const hide = notificationService.loading('正在处理...')
// 手动关闭
hide()
```

### 3. 通知提醒（Notification）

适用于需要详细描述的重要信息：

```typescript
// 成功通知
notificationService.notifySuccess('操作成功', '您的数据已成功保存')

// 信息通知
notificationService.notifyInfo('系统提示', '有新的消息等待查看')

// 警告通知
notificationService.notifyWarning('注意', '您的会话即将过期')

// 错误通知
notificationService.notifyError('错误', '网络连接失败，请检查网络设置')
```

### 4. 智能错误处理

使用 `handleError` 方法可以根据错误类型和严重程度自动选择合适的提示方式：

```typescript
import { ErrorType, ErrorSeverity } from '../services/notificationService'

notificationService.handleError({
  type: ErrorType.NETWORK,
  severity: ErrorSeverity.MEDIUM,
  message: '网络连接失败',
  details: '请检查您的网络连接',
  retryable: true,
  action: () => retryConnection(),
  actionText: '重试'
})
```

## 错误类型和严重程度

### 错误类型（ErrorType）

- `NETWORK` - 网络错误
- `WEBSOCKET` - WebSocket连接错误
- `AUTH` - 认证错误
- `VALIDATION` - 验证错误
- `MESSAGE_SEND` - 消息发送错误
- `API` - API调用错误
- `UNKNOWN` - 未知错误

### 严重程度（ErrorSeverity）

- `LOW` - 低级别（使用Message，4秒显示）
- `MEDIUM` - 中级别（使用Message，6秒显示）
- `HIGH` - 高级别（使用Notification，8秒显示）
- `CRITICAL` - 严重级别（使用Notification，不自动关闭）

## 配置选项

### 消息配置

```typescript
notificationService.success('成功', {
  duration: 5, // 显示时长（秒）
})
```

### 通知配置

```typescript
notificationService.notifySuccess('成功', '详细描述', {
  duration: 10, // 显示时长（秒）
  placement: 'topRight' // 显示位置
})
```

## 在聊天界面中的应用

聊天界面已经集成了提示功能，包括：

1. **连接状态提示** - WebSocket连接成功/失败
2. **加载状态提示** - 联系人列表加载状态
3. **消息发送反馈** - 发送成功/失败提示
4. **输入验证** - 消息内容验证提示
5. **错误重试** - 支持重试失败的操作

## 演示功能

在聊天界面中点击"显示提示演示"按钮可以查看各种提示效果。

## 最佳实践

1. **选择合适的提示类型**
   - 简短反馈使用 Message
   - 重要信息使用 Notification

2. **合理设置显示时长**
   - 成功信息：3-4秒
   - 警告信息：5-6秒
   - 错误信息：6-8秒
   - 严重错误：不自动关闭

3. **提供重试机制**
   - 网络错误应提供重试选项
   - 临时性错误支持自动重试

4. **避免过度提示**
   - 不要为每个操作都显示提示
   - 合并相似的提示信息

## API 参考

详细的 API 文档请参考 `src/renderer/src/services/notificationService.ts` 文件中的类型定义和注释。
