<!-- 消息输入组件 -->
<template>
  <div class="border-t border-gray-300 bg-[#ebeff5] p-4 h-40 flex-shrink-0">
    <div class="flex items-center justify-between mb-1">
      <div class="flex items-center gap-2">
        <button
          class="bg-transparent border-none cursor-pointer p-2 rounded text-gray-500 text-base hover:bg-gray-100"
          @click="$emit('insert-emoji')"
        >
          <img src="../assets/editor/insert-emoji.svg" alt="表情" class="w-5 h-5" />
        </button>
        <button
          class="bg-transparent border-none cursor-pointer p-2 rounded text-gray-500 text-base hover:bg-gray-100"
          @click="$emit('screenshot')"
        >
          <img src="../assets/editor/screenshot.svg" alt="截图" class="w-5 h-5" />
        </button>
        <button
          class="bg-transparent border-none cursor-pointer p-2 rounded text-gray-500 text-base hover:bg-gray-100"
          @click="$emit('attach-file')"
        >
          <img src="../assets/editor/attach-file.svg" alt="文件" class="w-5 h-5" />
        </button>
      </div>
    </div>
    <div class="relative rounded-md">
      <textarea
        id="messageBox"
        ref="messageEditor"
        v-model="messageContent"
        class="w-full border-none outline-none p-1 pr-10 pb-12 text-sm font-inherit resize-none min-h-28 placeholder-gray-400 bg-transparent"
        placeholder="输入消息... (Enter发送，Ctrl+Enter换行)"
        rows="6"
        @keydown="handleKeyDown"
      ></textarea>
      <button
        id="sendBtn"
        class="absolute bottom-2 right-2 bg-[#214d91] text-white border-none px-4 py-2 rounded-md cursor-pointer text-sm font-medium hover:bg-[#2d6bc9] disabled:bg-[#b1c3e0] disabled:cursor-not-allowed"
        :disabled="!messageContent.trim()"
        @click="handleSend"
      >
        发送
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { User } from '../api'

interface Contact {
  id: string
  name: string
  avatar: string
  status: string
  lastMessage: string
  lastMessageTime: Date
  user: User
}

interface Props {
  currentContact: Contact | null
}

defineProps<Props>()

const emit = defineEmits<{
  'send-message': [content: string]
  'insert-emoji': []
  'attach-file': []
  screenshot: []
}>()

const messageContent = ref('')
const messageEditor = ref<HTMLTextAreaElement>()

const handleSend = () => {
  if (!messageContent.value.trim()) {
    return
  }

  // 发送消息 - 保持换行符，但移除首尾空白
  const content = messageContent.value.replace(/^\s+|\s+$/g, '')
  messageContent.value = ''

  // 触发发送事件
  emit('send-message', content)
}

const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    if (event.ctrlKey) {
      // Ctrl+Enter: 插入换行符
      event.preventDefault()
      const textarea = event.target as HTMLTextAreaElement
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const value = textarea.value

      // 在光标位置插入换行符
      messageContent.value = value.substring(0, start) + '\n' + value.substring(end)

      // 设置光标位置到换行符后面
      setTimeout(() => {
        textarea.selectionStart = textarea.selectionEnd = start + 1
      }, 0)
    } else {
      // Enter: 发送消息
      event.preventDefault()
      handleSend()
    }
  }
}

// 暴露方法给父组件
defineExpose({
  focus: () => messageEditor.value?.focus(),
  clear: () => {
    messageContent.value = ''
  }
})
</script>
